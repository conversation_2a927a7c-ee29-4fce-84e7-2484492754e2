{"cells": [{"cell_type": "markdown", "id": "28761337-6f9e-4ffd-bb0b-8b7d97124d5d", "metadata": {}, "source": ["# 控制LED灯颜色\n", "\n", "同济子豪兄 2024-4-29"]}, {"cell_type": "markdown", "id": "5d90ce6f-e054-4ac9-b01c-7064ba4fb8ef", "metadata": {}, "source": ["## 导入工具包"]}, {"cell_type": "code", "execution_count": 1, "id": "568dd055-d88c-4814-8abd-1c599947c807", "metadata": {}, "outputs": [], "source": ["from pymycobot.mycobot import MyCobot\n", "from pymycobot import PI_PORT, PI_BAUD\n", "import time"]}, {"cell_type": "markdown", "id": "3e304a82-9819-4f59-a54a-6e7fff17f139", "metadata": {}, "source": ["## 连接机械臂"]}, {"cell_type": "code", "execution_count": 2, "id": "c4ba0811-fd4e-44ea-95c9-a85f5b0d0068", "metadata": {}, "outputs": [], "source": ["mc = MyCobot(PI_PORT, PI_BAUD)"]}, {"cell_type": "markdown", "id": "6c397d53-f75f-4186-a9d1-f83e0b7d3aa8", "metadata": {}, "source": ["## 设置LED灯的RGB颜色"]}, {"cell_type": "code", "execution_count": 3, "id": "cbd6e9d0-87ca-43c9-983f-88c49be3b6cf", "metadata": {}, "outputs": [], "source": ["# 红灯\n", "mc.set_color(255, 0, 0)"]}, {"cell_type": "code", "execution_count": 4, "id": "165723ab-12f2-47c1-87e8-742a098c0980", "metadata": {}, "outputs": [], "source": ["# 蓝灯\n", "mc.set_color(0, 0, 255)"]}, {"cell_type": "code", "execution_count": 5, "id": "e9279948-09b5-403a-9654-2589dd4701ec", "metadata": {}, "outputs": [], "source": ["# 绿灯\n", "mc.set_color(0, 255, 0)"]}, {"cell_type": "code", "execution_count": 6, "id": "2738b2ee-fc24-4cc5-8933-0b931bd5daf8", "metadata": {}, "outputs": [], "source": ["# 白色\n", "mc.set_color(255, 255, 255)"]}, {"cell_type": "code", "execution_count": 7, "id": "a6b867fc-61ba-46f4-900d-fc5be819016c", "metadata": {}, "outputs": [], "source": ["# 橙色\n", "mc.set_color(225, 103, 36)"]}, {"cell_type": "markdown", "id": "80276e91-b107-4ec3-9e88-62170642b994", "metadata": {}, "source": ["## Demo：LED灯颜色循环"]}, {"cell_type": "code", "execution_count": 8, "id": "6d821085-94c4-4715-bee9-0f44e3c5ff6f", "metadata": {}, "outputs": [], "source": ["i = 7 # 循环次数\n", "\n", "while i > 0:                            \n", "    time.sleep(1.5)         \n", "    mc.set_color(255, 0, 0) #红灯亮\n", "    time.sleep(2)\n", "    mc.set_color(0, 255, 0) #绿灯亮\n", "    time.sleep(3)\n", "    mc.set_color(0, 0, 255) #蓝灯亮\n", "    i -= 1"]}, {"cell_type": "markdown", "id": "dd0e9279-0b8d-4c2a-9163-0cbfee2a09b8", "metadata": {}, "source": ["## 参考资料\n", "\n", "官方样例代码\n", "\n", "https://docs.elephantrobotics.com/docs/mycobot-pi-cn/7-ApplicationBasePython/7.8_example.html#1-%E6%8E%A7%E5%88%B6rgb%E7%81%AF%E6%9D%BF"]}, {"cell_type": "code", "execution_count": null, "id": "a4dc7ecb-8d1f-441d-9680-ea791ad80f9a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}