# utils_tts.py
# 同济子豪兄 2024-5-23
# 语音合成

print('导入语音合成模块')

import os
import appbuilder
from API_KEY import *
import pyaudio
import wave

try:
    tts_ab = appbuilder.TTS()
    TTS_AVAILABLE = True
except Exception as e:
    print(f'TTS初始化失败: {e}')
    TTS_AVAILABLE = False

def tts(TEXT='我是同济子豪兄的麒麟臂', tts_wav_path = 'temp/tts.wav'):
    '''
    语音合成TTS，生成wav音频文件
    '''
    if not TTS_AVAILABLE:
        print(f'[模拟] TTS语音合成: {TEXT}')
        print('请检查API_KEY.py中的APPBUILDER_TOKEN是否正确配置')
        # 创建一个有效的wav文件作为占位符
        import wave
        import numpy as np

        # 确保temp目录存在
        os.makedirs('temp', exist_ok=True)

        # 生成一段静音音频（1秒）
        sample_rate = 16000
        duration = 1.0
        samples = int(sample_rate * duration)
        silence = np.zeros(samples, dtype=np.int16)

        with wave.open(tts_wav_path, 'w') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(silence.tobytes())
        return

    try:
        inp = appbuilder.Message(content={"text": TEXT})
        out = tts_ab.run(inp, model="paddlespeech-tts", audio_type="wav")
        # out = tts_ab.run(inp, audio_type="wav")
        with open(tts_wav_path, "wb") as f:
            f.write(out.content["audio_binary"])
        # print("TTS语音合成，导出wav音频文件至：{}".format(tts_wav_path))
    except Exception as e:
        print(f'TTS语音合成失败: {e}')
        print(f'[模拟] TTS语音合成: {TEXT}')
        # 创建一个有效的wav文件作为占位符
        import wave
        import numpy as np

        # 确保temp目录存在
        os.makedirs('temp', exist_ok=True)

        # 生成一段静音音频（1秒）
        sample_rate = 16000
        duration = 1.0
        samples = int(sample_rate * duration)
        silence = np.zeros(samples, dtype=np.int16)

        with wave.open(tts_wav_path, 'w') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(silence.tobytes())

def play_wav(wav_file='asset/welcome.wav'):
    '''
    播放wav音频文件
    '''
    # 检查操作系统，使用相应的播放命令
    if os.name == 'posix':  # Linux/Unix系统
        prompt = 'aplay -t wav {} -q'.format(wav_file)
        os.system(prompt)
    else:  # Windows系统
        try:
            import pygame
            pygame.mixer.init()
            pygame.mixer.music.load(wav_file)
            pygame.mixer.music.play()
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)
        except ImportError:
            print(f'    [模拟] Windows系统播放音频: {wav_file}')
            print('    提示：安装pygame可以实现真实音频播放: pip install pygame')

# def play_wav(wav_file='temp/tts.wav'):
#     '''
#     播放wav文件
#     '''
#     wf = wave.open(wav_file, 'rb')
 
#     # 实例化PyAudio
#     p = pyaudio.PyAudio()
 
#     # 打开流
#     stream = p.open(format=p.get_format_from_width(wf.getsampwidth()),
#                     channels=wf.getnchannels(),
#                     rate=wf.getframerate(),
#                     output=True)

#     chunk_size = 1024
#     # 读取数据
#     data = wf.readframes(chunk_size)
 
#     # 播放音频
#     while data != b'':
#         stream.write(data)
#         data = wf.readframes(chunk_size)
 
#     # 停止流，关闭流和PyAudio
#     stream.stop_stream()
#     stream.close()
#     p.terminate()