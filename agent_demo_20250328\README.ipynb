{"cells": [{"cell_type": "markdown", "id": "c8a0febf-1001-4a87-b873-06bc1471187c", "metadata": {}, "source": ["# 语音控制智能体\n", "\n", "同济子豪兄 2024-5-23"]}, {"cell_type": "markdown", "id": "bb2091f1-1d00-40bc-9432-9d7cd3d9157e", "metadata": {}, "source": ["## 首先要做\n", "\n", "- 音频输出选择HDMI显示屏\n", "\n", "- 找到麦克风设备号\n", "\n", "- 手眼标定"]}, {"cell_type": "markdown", "id": "e2c144d1-059c-40d1-b69b-8485cb6686c5", "metadata": {}, "source": ["# 智能体Agent能够调用的函数"]}, {"cell_type": "code", "execution_count": 2, "id": "1b933878-c06f-426d-8ca3-d4b5ddade0ac", "metadata": {}, "outputs": [], "source": ["# 函数一：归零\n", "# back_zero()\n", "\n", "# 函数二：放松机械臂\n", "# relax_arms()\n", "\n", "# 函数三：摇头\n", "# head_shake()\n", "\n", "# 函数四：点头\n", "# head_nod()\n", "\n", "# 函数五：跳舞\n", "# head_dance()\n", "\n", "# 函数六：开启吸泵\n", "# pump_on()\n", "\n", "# 函数七：关闭吸泵\n", "# pump_off()\n", "\n", "# 函数八：移动到指定坐标\n", "# move_to_coords(X=150, Y=-120)\n", "\n", "# 函数九：指定关节旋转\n", "# single_joint_move(1, 60)\n", "\n", "# 函数十：移动至俯视姿态\n", "# move_to_top_view()\n", "\n", "# 函数十一：拍一张俯视图\n", "# top_view_shot()\n", "\n", "# 函数十二：开启摄像头\n", "# check_camera()\n", "\n", "# 函数十三：LED灯变颜色\n", "# llm_led('帮我把LED灯的颜色改为贝加尔湖的颜色')\n", "\n", "# 函数十四：移动物体\n", "# vlm_move(PROMPT='帮我把红色方块放在小猪佩奇上')\n", "\n", "# 函数十五：拖动示教\n", "# drag_teach()\n", "\n", "# 函数十六：休息等待\n", "# time.sleep()"]}, {"cell_type": "code", "execution_count": null, "id": "d1d0c3be-3080-4543-a943-adb10e19e79b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}