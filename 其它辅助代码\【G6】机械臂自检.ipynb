{"cells": [{"cell_type": "markdown", "id": "e82a608f-b566-4e61-bf66-a3c009d9d835", "metadata": {}, "source": ["# 机械臂自检\n", "\n", "同济子豪兄 2024-5-10"]}, {"cell_type": "markdown", "id": "26c04aaa-d29b-4f0d-95a6-08a9ea2e0057", "metadata": {}, "source": ["## 导入工具包"]}, {"cell_type": "code", "execution_count": 2, "id": "3c0b3e7b-643f-450d-a8df-dccab3bcfc09", "metadata": {}, "outputs": [], "source": ["from pymycobot.mycobot import MyCobot\n", "from pymycobot import PI_PORT, PI_BAUD\n", "import time"]}, {"cell_type": "markdown", "id": "b78cf204-e194-409e-96fc-e58b2bf096f3", "metadata": {}, "source": ["## 连接机械臂"]}, {"cell_type": "code", "execution_count": 3, "id": "623fbc87-eabb-4bbd-a3fb-2e773cceb43a", "metadata": {}, "outputs": [], "source": ["mc = MyCobot(PI_PORT, PI_BAUD)"]}, {"cell_type": "code", "execution_count": 4, "id": "761fe32b-c32e-4fef-a46e-4cb4c8ef4c7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["6.4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看固件版本\n", "mc.get_system_version()"]}, {"cell_type": "code", "execution_count": null, "id": "253350cf-41dd-44ab-a5bd-bd6506087242", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc12f6be-d99f-4bab-a31a-ece0fa44c417", "metadata": {}, "outputs": [], "source": ["mc.is_controller_connected()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}